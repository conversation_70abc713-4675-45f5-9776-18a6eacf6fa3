;============================================================
; madflight PlatformIO Project Configuration File
;
; For madflight documentation see:
; https://madflight.com
;
; For PlatformIO documentation see:
; https://docs.platformio.org/page/projectconf.html
;============================================================

[platformio]
; Uncomment ONE example to build:
; Note: you might need to execute "Clean" or delete the .pio folder when you change src_dir
;src_dir = 00.HelloWorld
;src_dir = 01.Quadcopter
src_dir = 02.QuadcopterAdvanced
;src_dir = 03.Plane

; Optionally set the default env(s) to compile, instead of "all environments"
;default_envs = esp32s3

[env]
; Include madflight library - uncomment ONE of the following lib_deps lines:
; Note: you might need to execute "Clean" or delete the .pio folder when you change lib_deps

; Latest madflight release from PlatformIO Registry
lib_deps = qqqlab/madflight 

; Use local copy of madflight repository. (If you cloned the madflight Github repository and opened the examples folder in PlatformIO, then you can use this to use the local madflight library files.)
;lib_deps = symlink://..

; Latest madflight development version
;lib_deps = https://github.com/qqqlab/madflight.git 

; Particular madflight tag or branch
;lib_deps = https://github.com/qqqlab/madflight.git#v2.1.0 

; Folder on local drive
;lib_deps = symlink://C:/path/to/lib/madflight

; Copy from folder on local drive
;lib_deps = file://C:/path/to/lib/madflight

; Default monitor speed for madflight
monitor_speed = 115200

;============================================================
; ESP32 TARGETS
;============================================================
; Note: Espressiv stopped Arduino framework support for PlatformIO. Arduino 2.x
; is the last supported framework version, but madflight should still compile
; with this.

[env:esp32]
board = esp32dev
platform = espressif32
framework = arduino

[env:esp32s3]
board = lolin_s3_mini
platform = espressif32
framework = arduino
build_flags =
    -DCONFIG_IDF_TARGET_ESP32S3
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1

;============================================================
; RP2040/RP2350 TARGETS
;============================================================
; Note: RP2040/RP2350 is not merged into mainline PlatformIO, see 
; https://arduino-pico.readthedocs.io/en/latest/platformio.html
; on Windows you need to enable long path names, see above link for instructions

[env:rp2040]
board = pico
platform = https://github.com/maxgerhardt/platform-raspberrypi.git
framework = arduino
board_build.core = earlephilhower

[env:rp2350A]
board = rpipico2
platform = https://github.com/maxgerhardt/platform-raspberrypi.git
framework = arduino
board_build.core = earlephilhower

[env:rp2350B]
board = solderparty_rp2350_stamp_xl
platform = https://github.com/maxgerhardt/platform-raspberrypi.git
framework = arduino
board_build.core = earlephilhower

;============================================================
; STM32 TARGETS
;============================================================

[env:stm32f411]
board = blackpill_f411ce
platform = ststm32
framework = arduino
lib_deps = 
    ${env.lib_deps}
    stm32duino/STM32duino FreeRTOS
build_flags =
    -DSERIAL_RX_BUFFER_SIZE=256
    -DSERIAL_TX_BUFFER_SIZE=256
