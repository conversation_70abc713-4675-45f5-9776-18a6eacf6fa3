/*
 * QuadFly Flight Controller - Configuration Header
 *
 * Contains all system constants, pin definitions, and configuration parameters
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>
#include <Wire.h>
#include <EEPROM.h>

// ESP32-specific includes
#ifndef LED_BUILTIN
#define LED_BUILTIN 2  // Default LED pin for ESP32
#endif

// Configuration version
#define CONFIG_VERSION 1

// System version
#define QUADFLY_VERSION "1.0.0"

// Hardware pin definitions - Updated for FS TH9X setup
#define SDA_PIN                 21  // I2C_SDA_PIN
#define SCL_PIN                 22  // I2C_SCL_PIN
#define GPS_RX_PIN              17
#define GPS_TX_PIN              16
#define BUZZER_PIN              18  // Buzzer/speaker
#define STATUS_LED_PIN          2   // Built-in LED
#define ERROR_LED_PIN           4   // External error LED (optional)

// RC Receiver PWM input pins (FS TH9X individual channels)
// NOTE: For madflight PPM mode, only one pin is used for all channels
#define RC_PPM_PIN              4   // PPM input pin (changed from 14 due to ESP32 restrictions)
#define RC_THROTTLE_PIN         14  // Individual channel pins (not used in PPM mode)
#define RC_ROLL_PIN             13
#define RC_PITCH_PIN            12
#define RC_YAW_PIN              27
#define RC_MODE_PIN             37
#define RC_AUX1_PIN             34
#define RC_AUX2_PIN             36

// ESC PWM output pins (motor control)
#define MOTOR_FR_PIN            32  // Front Right
#define MOTOR_FL_PIN            33  // Front Left (changed to avoid conflict)
#define MOTOR_RR_PIN            19  // Rear Right
#define MOTOR_RL_PIN            15  // Rear Left


// Battery monitoring
#define BATTERY_VOLTAGE_PIN     35
#define BATTERY_CURRENT_PIN     35

// Task update rates (milliseconds)
#define SENSOR_UPDATE_RATE_MS   5   // 200Hz
#define CONTROL_UPDATE_RATE_MS  10  // 100Hz
#define SAFETY_UPDATE_RATE_MS   20  // 50Hz

// Communication settings
#define GPS_BAUD_RATE           38400 // 38400 for NEO-6M

// Sensor addresses (I2C)
#define MPU6050_ADDRESS         0x68
#define HMC5883L_ADDRESS        0x1E
#define QMC5883L_ADDRESS        0x0D  // Alternative magnetometer address
#define MS5611_ADDRESS          0x77

// Motor behavior
#ifndef MOTOR_START_PWM
#define MOTOR_START_PWM 1150  // Motors remain stopped until throttle > this PWM
#endif


// RC Channel mapping for FS TH9X
#define RC_CHANNEL_THROTTLE     0
#define RC_CHANNEL_ROLL         1
#define RC_CHANNEL_PITCH        2
#define RC_CHANNEL_YAW          3
#define RC_CHANNEL_MODE         4
#define RC_CHANNEL_AUX1         5
#define RC_CHANNEL_AUX2         6

// RC PWM signal ranges (typical for FS TH9X)
#define RC_PWM_MIN              1000  // Minimum PWM value
#define RC_PWM_MAX              2000  // Maximum PWM value
#define RC_PWM_CENTER           1500  // Center/neutral PWM value
#define RC_PWM_DEADBAND         20    // Deadband around center
#define RC_SIGNAL_TIMEOUT_MS    100   // Signal loss timeout

// Flight mode definitions
typedef enum {
  FLIGHT_MODE_DISARMED = 0,
  FLIGHT_MODE_MANUAL,
  FLIGHT_MODE_STABILIZE,
  FLIGHT_MODE_ALT_HOLD,
  FLIGHT_MODE_GPS_HOLD,
  FLIGHT_MODE_RTH,
  FLIGHT_MODE_EMERGENCY
} FlightMode_t;

// System state structure
typedef struct {
  FlightMode_t flightMode;
  FlightMode_t previousFlightMode;
  bool armed;
  bool calibrationMode;
  bool emergencyMode;
  bool gpsLock;
  bool receiverConnected;
  uint8_t systemHealth;
  uint32_t uptime;
  uint32_t flightTime;
} SystemState_t;

// Sensor data structure
typedef struct {
  // IMU data
  float accelX, accelY, accelZ;    // m/s²
  float gyroX, gyroY, gyroZ;       // rad/s
  float magX, magY, magZ;          // µT
  float temperature;               // °C

  // Barometer data
  float pressure;                  // Pa
  float baroAltitude;             // m

  // Calibration offsets
  float accelOffset[3];
  float gyroOffset[3];
  float magOffset[3];
  float magScale[3];

  // Data quality
  bool dataValid;
  uint32_t lastUpdate;
} SensorData_t;

// Attitude data structure
typedef struct {
  float roll, pitch, yaw;          // radians
  float rollRate, pitchRate, yawRate; // rad/s
  float quaternion[4];             // w, x, y, z
} AttitudeData_t;

// GPS data structure
typedef struct {
  double latitude, longitude;      // degrees
  float altitude;                  // m
  float speed;                     // m/s
  float course;                    // degrees
  uint8_t satellites;
  bool fix;
  float hdop;
  uint32_t lastUpdate;
} GPSData_t;

// Receiver data structure
typedef struct {
  uint16_t channels[8];            // PWM values (1000-2000µs)
  bool signalLost;
  uint8_t signalQuality;
  uint32_t lastUpdate;
} ReceiverData_t;

// Motor output structure
typedef struct {
  uint16_t motor[4];               // PWM values (1000-2000µs)
  bool motorsArmed;
} MotorData_t;

// Battery data structure
typedef struct {
  float voltage;                   // V
  float current;                   // A
  float capacity;                  // mAh
  uint8_t percentage;              // %
  bool lowVoltageWarning;
  bool criticalVoltageWarning;
} BatteryData_t;

// Altitude data structure
typedef struct {
  float altitude;                  // m (relative to takeoff)
  float velocity;                  // m/s (vertical velocity)
  float rawBaroAltitude;           // m (raw barometer altitude)
  uint32_t lastUpdate;
} AltitudeData_t;

// Main flight data structure
typedef struct {
  SensorData_t sensors;
  AttitudeData_t attitude;
  GPSData_t gps;
  ReceiverData_t receiver;
  MotorData_t motors;
  BatteryData_t battery;
  AltitudeData_t altitude;
} FlightData_t;

// PID parameters structure
typedef struct {
  float kp, ki, kd;
  float integralLimit;
  float outputLimit;
} PIDParams_t;

// Configuration data structure
typedef struct {
  // PID parameters
  PIDParams_t rollPID;
  PIDParams_t pitchPID;
  PIDParams_t yawPID;
  PIDParams_t altPID;
  PIDParams_t velPID;
  PIDParams_t posPID;

  // Receiver configuration
  uint16_t rcMin[8];
  uint16_t rcMax[8];
  uint16_t rcMid[8];
  uint8_t rcDeadband;

  // Safety parameters
  float maxTiltAngle;              // degrees
  float maxVerticalSpeed;          // m/s
  uint16_t failsafeThrottle;
  uint16_t lowVoltageThreshold;    // mV
  uint16_t criticalVoltageThreshold; // mV

  // Flight parameters
  uint8_t motorIdleSpeed;          // %
  bool enableGPS;
  bool enableBarometer;
  bool enableMagnetometer;

  // Calibration data
  bool sensorsCalibrated;
  bool escCalibrated;
  bool receiverCalibrated;
  float gyroCalibration[3];
  float accelCalibration[3];
  float magOffset[3];
  float magScale[3];
  float baroReferencePressure;

  // Configuration version
  uint8_t configVersion;
  // Checksum for integrity validation (must be the last field used in checksum calculation)
  uint32_t checksum;
} ConfigData_t;

// Default configuration values
#define DEFAULT_ROLL_KP         0.8f
#define DEFAULT_ROLL_KI         0.02f
#define DEFAULT_ROLL_KD         0.15f
#define DEFAULT_PITCH_KP        0.8f
#define DEFAULT_PITCH_KI        0.02f
#define DEFAULT_PITCH_KD        0.15f
#define DEFAULT_YAW_KP          1.2f
#define DEFAULT_YAW_KI          0.05f
#define DEFAULT_YAW_KD          0.0f
#define DEFAULT_ALT_KP          0.5f
#define DEFAULT_ALT_KI          0.1f
#define DEFAULT_ALT_KD          0.2f

#define DEFAULT_VEL_KP          0.3f
#define DEFAULT_VEL_KI          0.05f
#define DEFAULT_VEL_KD          0.1f

#define DEFAULT_POS_KP          0.2f
#define DEFAULT_POS_KI          0.01f
#define DEFAULT_POS_KD          0.05f

#define DEFAULT_MAX_TILT_ANGLE  45.0f
#define DEFAULT_MAX_VERTICAL_SPEED 5.0f
#define DEFAULT_FAILSAFE_THROTTLE  1100
#define DEFAULT_LOW_VOLTAGE     3300    // 3.3V per cell
#define DEFAULT_CRITICAL_VOLTAGE 3000   // 3.0V per cell

#define DEFAULT_RC_MIN          1000
#define DEFAULT_RC_MAX          2000
#define DEFAULT_RC_MID          1500
#define DEFAULT_RC_DEADBAND     10

// EEPROM addresses
#define EEPROM_CONFIG_ADDRESS   0
#define EEPROM_CONFIG_SIZE      sizeof(ConfigData_t)

// Mathematical constants
#ifndef PI
#define PI 3.14159265359f
#endif

#ifndef DEG_TO_RAD
#define DEG_TO_RAD (PI / 180.0f)
#endif

#ifndef RAD_TO_DEG
#define RAD_TO_DEG (180.0f / PI)
#endif

// System health bit flags
#define HEALTH_IMU_OK       0x01
#define HEALTH_BARO_OK      0x02
#define HEALTH_MAG_OK       0x04
#define HEALTH_GPS_OK       0x08
#define HEALTH_RECEIVER_OK  0x10
#define HEALTH_BATTERY_OK   0x20

// Configuration management functions
bool loadConfiguration(void);
bool saveConfiguration(void);
void resetConfiguration(void);
void getPIDGains(float* p, float* i, float* d);
void setPIDGains(float* p, float* i, float* d);
void getGyroCalibration(float* calibration);
void setGyroCalibration(float* calibration);
void getAccCalibration(float* calibration);
void setAccCalibration(float* calibration);

// Global configuration instance (defined in config.cpp)
extern ConfigData_t currentConfig;

// Global system data instances (defined in QuadFly_Main.ino)
extern SensorData_t systemSensors;
extern AttitudeData_t systemAttitude;
extern GPSData_t systemGPS;
extern ReceiverData_t systemReceiver;
extern MotorData_t systemMotors;
extern BatteryData_t systemBattery;
extern AltitudeData_t systemAltitude;
extern SystemState_t systemState;

#endif // CONFIG_H