/*========================================================================================================================
                                              MADFLIGHT CONFIG
==========================================================================================================================

The configuration is loaded from two strings: first from madflight_board in from the board header in /brd/, and then from 
madflight_config in this file. If the same setting occurs more than once, the last one is applied.

The strings are multi-line raw strings in with a key-value list. Anything after '#' or '/' is ignored as comment.

You have 3 options to setup the flight controller:

  1) Default - Keep #define MF_BOARD "brd/default.h", this defines the default pinout as shown on https://madflight.com 
     for the supported processor families. Now edit madflight_config below, and uncomment and configure imu_gizmo, 
     imu_bus_type, rcl_gizmo, and other lines as needed. (Do not change the pin_xxx_yyy and xxx_yyy_bus settings.)
 
  2) BetaFlight - Change "brd/default.h" to the BetaFlight flight controller you want to use, for example: 
     #define MF_BOARD "brd/betaflight/MTKS-MATEKH743.h". See library/madflight/src for all available boards. Edit 
     madflight_config to fine-tune the configuration.
 
  3) Bare Metal - Comment out "#define MF_BOARD", and set the full configuration in madflight_config below.

Pins and spi/i2c/serial busses use zero-based numbering, i.e. "gps_ser_bus 0" connects the GPS to the first serial bus
with pins pin_ser0_tx and pin_ser0_rx. Pins use GPIO numbers, not physical pin numbers. Use -1 to disable a pin or bus.

You can also modify the configuration from the CLI, for example "set imu_gizmo MPU6500" or "set imu_spi_bus 1", then
use "save" to save the config to eeprom and reboot to use the new config.

If things do not work as expected, have a good look at the startup messages!

========================================================================================================================*/

//define the board header to use, or comment out for none
#define MF_BOARD "brd/default.h"

const char madflight_config[] = R""(

//--- IMU --- Inertial Measurement Unit  (use spi -OR- i2c bus)
imu_gizmo      MPU6050    // Using MPU6050 as specified in config.md
imu_bus_type   I2C        // Using I2C bus
imu_align      CW0        // Default alignment
//imu_spi_bus    -1 //spi
//pin_imu_cs     -1 //spi
//pin_imu_int    -1 //spi and i2c
imu_i2c_bus    0          // Using I2C bus 0
imu_i2c_adr    104        // MPU6050 address 0x68 = 104 decimal

// IMPORTANT: the IMU sensor should be the ONLY sensor on the selected bus


//--- RCL --- Remote Controller Link  (use serial bus -OR- ppm pin)
rcl_gizmo      PPM        // Using PPM for FS TH9X receiver
rcl_num_ch     7          // 7 channels as defined in config.md
rcl_deadband   20         // RC_PWM_DEADBAND from config.md
//rcl_ser_bus   -1
pin_rcl_ppm   14          // RC_THROTTLE_PIN as main PPM input

//--- BAR --- Barometer
bar_gizmo      MS5611     // Using MS5611 as specified in config.md
bar_i2c_adr    119        // MS5611 address 0x77 = 119 decimal
bar_i2c_bus   0           // Using I2C bus 0

//--- MAG --- Magnetometer
mag_gizmo      QMC5883    // Using QMC5883 as alternative magnetometer
mag_align      CW0        // Default alignment
mag_i2c_adr    13         // QMC5883L address 0x0D = 13 decimal
mag_i2c_bus   0           // Using I2C bus 0

//--- BAT --- Battery Monitor  (use i2c bus -OR- adc pins)
bat_gizmo      ADC        // Using ADC for battery monitoring
//bat_i2c_adr    0
//bat_i2c_bus   -1
//pin_bat_i     35         // BATTERY_CURRENT_PIN (same as voltage in config.md)
pin_bat_v     35          // BATTERY_VOLTAGE_PIN from config.md
bat_cal_v      1          // Default voltage scale
//bat_cal_i,     1         // Default current scale

//--- GPS ---
gps_gizmo      UBLOX      // Using UBLOX GPS
gps_baud       38400      // GPS_BAUD_RATE from config.md
gps_ser_bus   0           // Using serial bus 0

//--- BBX --- Black Box Data Logger  (use spi -OR- mmc)
//bbx_gizmo      NONE  // options: NONE, SDSPI, SDMMC
//pin_bbx_cs    -1  // spi
//bbx_spi_bus   -1  // spi
//pin_mmc_dat   -1  // mmc
//pin_mmc_clk   -1  // mmc
//pin_mmc_cmd   -1  // mmc

//--- RDR --- Radar (use serial bus -OR- trig+echo pins)
//rdr_gizmo      NONE  // options: NONE, LD2411S, LD2413, USD1, SR04
//rdr_baud       0
//rdr_ser_bus   -1
//pin_rdr_trig  -1
//pin_rdr_echo  -1

//--- LED ---
led_gizmo     HIGH_IS_ON // Status LED configuration
pin_led       2          // STATUS_LED_PIN from config.md (built-in LED)

//--- AHR --- AHRS (keep MAHONY, unless you want to experiment)
//ahr_gizmo      MAHONY  // options: MAHONY, MAHONY_BF, MADGWICK, VQF

//--- Serial bus 0 --- (GPS)
pin_ser0_rx   17          // GPS_RX_PIN from config.md
pin_ser0_tx   16          // GPS_TX_PIN from config.md

//--- Serial bus 1 ---
//pin_ser1_rx   -1
//pin_ser1_tx   -1

//--- SPI bus 0 ---
//pin_spi0_miso -1
//pin_spi0_mosi -1
//pin_spi0_sclk -1

//--- SPI bus 1 ---
//pin_spi1_miso -1
//pin_spi1_mosi -1
//pin_spi1_sclk -1

//--- I2C Bus 0 --- (IMU, Barometer, Magnetometer)
pin_i2c0_sda  21          // SDA_PIN from config.md
pin_i2c0_scl  22          // SCL_PIN from config.md

//--- I2C Bus 1 ---
//pin_i2c1_sda  -1
//pin_i2c1_scl  -1

//--- OUT Pins --- (Motors and other outputs)
pin_out0      32          // MOTOR_FR_PIN - Front Right motor
pin_out1      33          // MOTOR_FL_PIN - Front Left motor
pin_out2      19          // MOTOR_RR_PIN - Rear Right motor
pin_out3      15          // MOTOR_RL_PIN - Rear Left motor
//pin_out4      -1
//pin_out5      -1
//pin_out6      -1
//pin_out7      -1
//pin_out8      -1
//pin_out9      -1
//pin_out10     -1
//pin_out11     -1
//pin_out12     -1
//pin_out13     -1
//pin_out14     -1
//pin_out15     -1

)""; // End of madflight_config


//========================================================================================================================//
//                                               COMPILER OPTIONS                                                         //
//========================================================================================================================//

// Reset config eeprom to defaults (uncomment this, upload, execute, then comment out, and upload again)
//#define MF_CONFIG_CLEAR

// Uncomment to print additional debug information and reduce startup delay
//#define MF_DEBUG

// Uncomment to enable Lua Scripting
//#define MF_LUA_ENABLE 1
